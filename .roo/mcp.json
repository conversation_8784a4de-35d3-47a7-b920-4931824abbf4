{"mcpServers": {"Sequential Thinking": {"command": "node", "args": ["/Users/<USER>/Desktop/test/mcp/node_modules/@modelcontextprotocol/server-sequential-thinking/dist/index.js"]}, "browser-tools-mcp": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp"]}, "Magic": {"command": "node", "args": ["/Users/<USER>/Desktop/test/mcp/node_modules/@21st-dev/magic/dist/index.js", "--config", "\"{\\\"TWENTY_FIRST_API_KEY\\\":\\\"b81f876436fd0e09998cea398c2bf417a88504544f6d0d4edcdd7d59cbcc5548\\\"}\""]}, "mcp-stock": {"command": "node", "args": ["/Users/<USER>/Documents/augment-projects/mcp-stock/build/index.js"]}, "WriteTime": {"command": "node", "args": ["/Users/<USER>/Documents/augment-projects/write-time/dist/index.js", "--config", "{\"username\":\"000381\",\"password\":\"gQGi8j5HqVsCBAfpARLD26ATbWdZmKzdThtZF3GRg7XkOS2tLF3Z+PKU8SuOpUsLIhvUkRWABWR1roeNpXJnYO15jRsdG9Afbk1EmPWHQ+MfLLYrDjDnPxe/OmGGoKAi8v+8pADkSU8sOGts0mTOH+o8MZfTKbQ76+LHe6RZD/XbPwNpvHOi+ZLzpKQDDiIV0OPSScpfxgrvkiz6gcJ3OOGCSOezkCGb59jTIzTaqGQFdatZYwrekQKSKgZg7ep7taWP2SlZMSNojpFgfKCgCPsY1bxcrP+SdSf//S+Kd94neBRq3wVloRKrvKzTaVt8P4oxaLsvUk9wcOCHS9KdsQ==\"}"]}, "context7": {"command": "node", "args": ["/Users/<USER>/Desktop/test/mcp/node_modules/@upstash/context7-mcp/dist/index.js"]}}}
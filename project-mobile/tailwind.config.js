/** @type {import('tailwindcss').Config} */
export default {
    content: [
      "./index.html",
      "./src/**/*.{vue,js,ts,jsx,tsx}",
    ],
    theme: {
      extend: {
        keyframes: {
          fadeInUp: {
            'from': {
              opacity: '0',
              transform: 'translateY(40px)'
            },
            'to': {
              opacity: '1',
              transform: 'translateY(0)'
            }
          }
        },
        animation: {
          fadeInUp: 'fadeInUp 0.6s ease-out forwards'
        }
      },
    },
    plugins: [],
    corePlugins: {
        preflight: false
      }
  }
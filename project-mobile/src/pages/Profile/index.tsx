// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.

import React from "react";
import {
  List,
  Avatar,
  Button,
  Dialog,
  Toast,
  Card,
  Tag,
  Grid,
} from "antd-mobile";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/useAuthStore";
import { getStorage } from "../../utils/storage";
import styles from "./index.module.css";
import {
  ClockCircleOutline,
  UnorderedListOutline,
  UserOutline,
  BellOutline,
  SetOutline,
  QuestionCircleOutline,
  TeamOutline,
} from "antd-mobile-icons";

interface UserInfo {
  username?: string;
  avatar?: string;
  department?: string;
  position?: string;
  totalHours?: number;
  weeklyHours?: number;
  monthlyHours?: number;
  projectCount?: number;
  teamCount?: number;
}

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { logout } = useAuthStore();
  const userInfo: UserInfo = JSON.parse(getStorage("userInfo") || "{}");

  const handleLogout = async () => {
    const result = await Dialog.confirm({
      content: "确定要退出登录吗？",
    });

    if (result) {
      logout();
      Toast.show({
        icon: "success",
        content: "已退出登录",
      });
      navigate("/login");
    }
  };

  const quickActions = [
    {
      icon: <ClockCircleOutline />,
      text: "填报工时",
      onClick: () => navigate("/timesheet"),
    },
    {
      icon: <UnorderedListOutline />,
      text: "去登陆",
      onClick: () => navigate("/login"),

    },
    {
      icon: <TeamOutline />,
      text: "请假",
      onClick: () => navigate("/leave"),
    },
    {
      icon: <BellOutline />,
      text: "通知",
      onClick: () => navigate("/message"),
    },
  ];

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.userCard}>
          <Avatar
            src=''
            fallback={userInfo.username?.[0]?.toUpperCase() || "U"}
            style={{
              "--size": "64px",
              fontSize: "28px",
              backgroundColor: "#1677ff",
            }}
            className={styles.avatar}
          />
          <div className={styles.userInfo}>
            <h2>{userInfo.username || "未知用户"}</h2>
          </div>
        </div>
      </div>

      <div className={styles.quickActions}>
        <Grid columns={4} gap={8}>
          {quickActions.map((action, index) => (
            <Grid.Item key={index}>
              <div className={styles.actionItem} onClick={action.onClick}>
                {action.icon}
                <span>{action.text}</span>
              </div>
            </Grid.Item>
          ))}
        </Grid>
      </div>

      <Card className={styles.statsCard}>
        <div className={styles.statsGrid}>
          <div className={styles.statsItem}>
            <span className={styles.statsValue}>
              {userInfo.totalHours || 0}
            </span>
            <span className={styles.statsLabel}>总工时</span>
          </div>
          <div className={styles.statsItem}>
            <span className={styles.statsValue}>
              {userInfo.weeklyHours || 0}
            </span>
            <span className={styles.statsLabel}>本周工时</span>
          </div>
          <div className={styles.statsItem}>
            <span className={styles.statsValue}>
              {userInfo.monthlyHours || 0}
            </span>
            <span className={styles.statsLabel}>本月工时</span>
          </div>
        </div>
      </Card>

      <List className={styles.menuList}>
        <List.Item
          prefix={<UserOutline />}
          onClick={() => Toast.show({ content: "功能开发中" })}
        >
          个人资料
        </List.Item>
        <List.Item
          prefix={<UnorderedListOutline />}
          onClick={() => navigate("/timesheet")}
        >
          工时记录
        </List.Item>
        <List.Item
          prefix={<BellOutline />}
          onClick={() =>navigate("/message")}
        >
          消息通知
          <Tag color="danger" fill="outline" className={styles.badge}>
            2
          </Tag>
        </List.Item>
      </List>

      <List className={styles.menuList}>
        <List.Item
          prefix={<SetOutline />}
          onClick={() => Toast.show({ content: "功能开发中" })}
        >
          系统设置
        </List.Item>
        <List.Item
          prefix={<QuestionCircleOutline />}
          onClick={() => navigate('/index-center')}
        >
          帮助与反馈
        </List.Item>
      </List>

      <Button
        block
        color="danger"
        className={styles.logoutButton}
        onClick={handleLogout}
      >
        退出登录
      </Button>
    </div>
  );
};

export default Profile;

// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.

import React, { useState } from "react";
import { Tabs, Avatar, Empty, Button } from "antd";
import type { TabsProps } from "antd";
import BottomNavBar from "../../components/BottomNavBar";

// Types
interface NotificationType {
  id: number;
  type: string;
  title: string;
  content: string;
  time: string;
  icon: string;
  iconColor: string;
  read: boolean;
  subContent?: string;
  subContentColor?: string;
}

interface ApprovalType {
  id: number;
  title: string;
  taskId: string;
  status: string;
  action: string;
  time: string;
  avatar: string;
  needAction: boolean;
  showButtons?: boolean;
}

// NotificationItem Component
const NotificationItem: React.FC<{ notification: NotificationType }> = ({ notification }) => {
  return (
    <div
      className={`relative p-4 mb-2 rounded-2xl transition-all duration-200 ${
        notification.read ? "bg-gray-50/80" : "bg-white"
      } border border-gray-100 shadow-sm hover:shadow-md`}
    >
      <div className="flex items-start space-x-4">
        <div
          className={`w-10 h-10 rounded-xl flex items-center justify-center transition-colors ${
            notification.iconColor === "amber"
              ? "bg-amber-50 text-amber-500"
              : notification.iconColor === "purple"
              ? "bg-purple-50 text-purple-500"
              : "bg-blue-50 text-blue-500"
          }`}
        >
          <i className={`text-lg fas fa-${notification.icon === "notification" ? "bell" : notification.icon === "user" ? "user" : "info-circle"}`}></i>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h3 className="text-sm font-semibold text-gray-900 line-clamp-1">
              {notification.title}
            </h3>
            <span className="text-[10px] text-gray-400 whitespace-nowrap flex-shrink-0">
              {notification.time}
            </span>
          </div>
          <p className="mt-1 text-sm text-gray-600 line-clamp-2">
            {notification.content}
          </p>
          {notification.subContent && (
            <div className={`mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              notification.subContentColor === "green" ? "bg-green-50 text-green-600" : "bg-gray-50 text-gray-600"
            }`}>
              {notification.subContent}
            </div>
          )}
        </div>
        {!notification.read && (
          <div className="absolute w-2 h-2 bg-red-500 rounded-full top-4 right-4"></div>
        )}
      </div>
    </div>
  );
};

// NotificationList Component
const NotificationList: React.FC<{ notifications: NotificationType[] }> = ({ notifications }) => {
  return (
    <div className="pt-4 space-y-4">
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  );
};

// ApprovalItem Component
const ApprovalItem: React.FC<{ approval: ApprovalType }> = ({ approval }) => {
  return (
    <div className="p-4 mb-3 transition-all duration-200 bg-white border border-gray-100 shadow-sm rounded-2xl hover:shadow-md">
      <div className="flex items-start space-x-4">
        <Avatar 
          src={approval.avatar} 
          size={40} 
          className="flex-shrink-0 border-2 rounded-xl border-gray-50"
        />
        <div className="flex-1 min-w-0">
          <div className="flex flex-col">
            <div className="flex items-start justify-between gap-2">
              <h3 className="text-sm font-semibold text-gray-900 line-clamp-1">
                {approval.title}
              </h3>
              <span className="text-[10px] text-gray-400 whitespace-nowrap">
                {approval.time}
              </span>
            </div>
            <div className="mt-1.5 space-y-1.5">
              <p className="text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-lg">
                {approval.taskId}
              </p>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-600">{approval.status}</span>
                <span className="inline-flex px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-600">
                  {approval.action}
                </span>
              </div>
            </div>

            {approval.showButtons && (
              <div className="flex justify-end mt-4 space-x-3">
                <Button
                  size="middle"
                  className="px-5 text-gray-600 border-gray-200 rounded-full hover:bg-gray-50 hover:border-gray-300"
                  onClick={()=>console.log('同意==========',approval)}
                >
                  不同意
                </Button>
                <Button
                  size="middle"
                  type="primary"
                  className="px-5 bg-blue-500 border-none rounded-full hover:bg-blue-600"
                  onClick={()=>console.log('不同意==========',approval)}

                >
                  同意
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// ApprovalList Component
const ApprovalList: React.FC<{ approvals: ApprovalType[] }> = ({ approvals }) => {
  return (
    <div className="pt-4 space-y-4">
      {approvals.map((approval) => (
        <ApprovalItem key={approval.id} approval={approval} />
      ))}
      <div className="py-3 text-center">
        <Button type="link" className="text-gray-500">
          查看更多
        </Button>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  const [activeTab, setActiveTab] = useState("1");

  // 未读消息列表数据
  const unreadNotifications = [
    {
      id: 1,
      type: "activity",
      title: "活动通知",
      content: "交付组长(调试)说您已被安排在项目11111111111活动",
      time: "1-22 17:13:51",
      icon: "notification",
      iconColor: "amber",
      read: false,
    },
    {
      id: 2,
      type: "activity",
      title: "活动通知",
      content: "请及时查收",
      time: "1-22 13:50:49",
      icon: "notification",
      iconColor: "amber",
      read: false,
    },
    {
      id: 3,
      type: "activity",
      title: "活动通知",
      content: "请及时查收",
      time: "1-21 15:10:28",
      icon: "notification",
      iconColor: "amber",
      read: false,
    },
    {
      id: 4,
      type: "completion",
      title: "交付组长(调试)提交的确认加班成果完成通知",
      content: "交付组长(调试)提交的确认加班成果",
      subContent: "审核通过",
      subContentColor: "green",
      time: "1-15 14:28:23",
      icon: "user",
      iconColor: "purple",
      read: false,
    },
    {
      id: 5,
      type: "completion",
      title: "交付组长(调试)提交的变更项目组长(产品)完成通知",
      content: "交付组长(调试)提交的变更项目组长(产品)",
      subContent: "审核通过",
      subContentColor: "green",
      time: "1-15 14:32:27",
      icon: "user",
      iconColor: "purple",
      read: false,
    },
    {
      id: 6,
      type: "completion",
      title: "交付组长(调试)提交的变更项目组长(产品)完成通知",
      content: "交付组长(调试)提交的变更项目组长(产品)",
      subContent: "审核通过",
      subContentColor: "green",
      time: "1-15 14:32:27",
      icon: "user",
      iconColor: "purple",
      read: true,
    },
  ];

  // 审批列表数据
  const approvals = [
    {
      id: 1,
      title: "销售组长(调试)提交的商机文件小组-工时审批",
      taskId: "工时审批: 销售组长(调试)任务工时3848",
      status: "审批阶段: 审批节点",
      action: "需要您审批",
      time: "1-15 13:38:59",
      avatar:
        "https://public.readdy.ai/ai/img_res/3f8d21074051dde9b060f1995aaed4f3.jpg",
      needAction: true,
    },
    {
      id: 2,
      title: "销售组长(调试)提交的商机文件小组-工时审批",
      taskId: "工时审批: 销售组长(调试)任务工时3846",
      status: "审批阶段: 审批节点",
      action: "需要您审批",
      time: "1-15 13:38:59",
      avatar:
        "https://public.readdy.ai/ai/img_res/3f8d21074051dde9b060f1995aaed4f3.jpg",
      needAction: true,
      showButtons: true,
    },
    {
      id: 3,
      title: "销售1(调试)提交的商机项目-工时审批",
      taskId: "工时审批: 销售1(调试)任务工时3880",
      status: "审批阶段: 项目组长审批",
      action: "需要您审批",
      time: "1-15 13:38:59",
      avatar:
        "https://public.readdy.ai/ai/img_res/b0e63a68a9bd1af4791209b42414803e.jpg",
      needAction: true,
    },
    {
      id: 4,
      title: "研发-副总监(调试)提交的项目加班审批流",
      taskId: "加班记录: 研发-副总监(调试)-加班项目加班项目(调试)",
      status: "审批阶段: 项目组长审批",
      action: "需要您审批",
      time: "1-15 13:38:11",
      avatar:
        "https://public.readdy.ai/ai/img_res/d7a9d1074051dde9b060f1995aaed4f3.jpg",
      needAction: true,
    },
  ];

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: `未读消息(${unreadNotifications.filter((n) => !n.read).length})`,
      children: <NotificationList notifications={unreadNotifications} />,
    },
    {
      key: "2",
      label: "审批列表",
      children: <ApprovalList approvals={approvals} />,
    },
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* 顶部标签栏 */}
      <div className="fixed top-0 left-0 right-0 z-50 border-b border-gray-100 bg-white/80 backdrop-blur-md">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={items}
          className="px-4"
          centered
        />
      </div>

      {/* 内容区域 */}
      <div className="flex-1 px-4 overflow-y-auto" style={{ paddingTop: '54px', paddingBottom: '70px' }}>
        {activeTab === "1" && unreadNotifications.length === 0 && (
          <Empty 
            description="暂无未读消息" 
            className="mt-20" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
        {activeTab === "2" && approvals.length === 0 && (
          <Empty 
            description="暂无审批" 
            className="mt-20"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
        {activeTab === "1" && <NotificationList notifications={unreadNotifications} />}
        {activeTab === "2" && <ApprovalList approvals={approvals} />}
      </div>

      {/* 底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 z-50 border-t border-gray-100 bg-white/80 backdrop-blur-md">
        <BottomNavBar />
      </div>
    </div>
  );
};

export default App;

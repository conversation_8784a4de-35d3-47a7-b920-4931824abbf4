import { createHashRouter } from 'react-router-dom';
import { PrivateRoute } from './components/PrivateRoute';
import TimesheetForm from './pages/timesheet';
import Login from './pages/login';
import Profile from './pages/Profile';
import Message from './pages/Message';
import Leave from './pages/Leave';
import IndexCenter from './pages/IndexCenter';

const router = createHashRouter([
  {
    path: '/',
    element: <PrivateRoute><Profile /></PrivateRoute>,
  },
  {
    path: '/timesheet',
    element: <PrivateRoute><TimesheetForm /></PrivateRoute>,
  },
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/profile',
    element: <PrivateRoute><Profile /></PrivateRoute>,
  },
  {
    path: '/leave',
    element: <PrivateRoute><Leave /></PrivateRoute>,
  },
  {
    path: '/message',
    element: <PrivateRoute><Message /></PrivateRoute>,
  },
  {
    path:'/index-center',
    element: <PrivateRoute><IndexCenter /></PrivateRoute>,
    
  }
]);

export default router; 
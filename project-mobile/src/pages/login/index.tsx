import { Form, Input, Button, Toast } from "antd-mobile";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../store/useAuthStore";
import { login } from "../../api";
import { setStorage } from "../../utils/storage";
import { encryptRSA } from "../../utils/rsa";

interface LoginForm {
  loginName: string;
  password: string;
}

export default function LoginPage() {
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  const [form] = Form.useForm();

  const onFinish = async (values: LoginForm) => {
    try {
      const encryptedPassword = encryptRSA(values.password);

      const response = await login({
        ...values,
        password: encryptedPassword,
      });

      console.log("登录结果:", response);

      if (Array.isArray(response.data?.msg) && response.data?.msg?.length) {
        Toast.show({
          icon: "fail",
          content: response.data.msg[0]?.error,
        });
        return;
      }

      if (response.success && response.data) {
        setStorage("token", response.data.token);
        setStorage("userId", response.data.userId);
        setStorage("loginRDTO", JSON.stringify(response.data.loginRDTO));
        setStorage("userInfo", JSON.stringify(response.data));

        setUser({
          id: response.data.userId,
          username: response.data.username || values.loginName,
          token: response.data.token,
        });

        Toast.show({
          icon: "success",
          content: "登录成功",
        });

        navigate("/");
      } else {
        Toast.show({
          icon: "fail",
          content: response.message || "登录失败",
        });
      }
    } catch (error) {
      console.error("登录出错:", error);
      Toast.show({
        icon: "fail",
        content: "登录失败，请稍后再试",
      });
    }
  };

  return (
    <div className="relative flex flex-col min-h-screen overflow-hidden bg-white">
      {/* 背景装饰 */}
      <div className="absolute -top-[10%] -left-[50%] w-[200%] h-[40%] bg-gradient-to-br from-green-500 to-blue-500 rounded-b-[50%] z-[1]"></div>

      {/* 头部 */}
      <div className="relative z-[2] text-center mt-15 mb-10 px-5">
        <h2 className="mb-2 text-3xl font-bold tracking-tight text-white">
          欢迎登录
        </h2>
        <p className="text-sm text-white/90">请输入您的账号和密码</p>
      </div>

      {/* 表单容器 */}
      <div className="relative z-[2] mx-4 p-6 bg-white rounded-3xl shadow-lg animate-fadeInUp">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          footer={
            <Button
              block
              type="submit"
              color="primary"
              size="large"
              className="!h-13 !mt-6 !text-base !font-semibold !text-white !border-none !rounded-xl !bg-gradient-to-br !from-green-500 !to-blue-500 !shadow-lg !shadow-blue-500/30 active:!scale-[0.98] active:!shadow-md active:!shadow-blue-500/20 transition-all duration-300"
            >
              登录
            </Button>
          }
        >
          <Form.Item
            name="loginName"
            label="账号"
            rules={[{ required: true, message: "请输入账号" }]}
            className="!mb-5"
          >
            <Input
              placeholder="请输入账号"
              className="!h-13 !text-[15px] !border-none !rounded-xl !bg-gray-50 !px-4 focus:!bg-white focus:!shadow-[inset_0_0_0_2px_#2196F3] transition-all duration-300"
            />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: "请输入密码" }]}
            className="!mb-5"
          >
            <Input
              type="password"
              placeholder="请输入密码"
              className="!h-13 !text-[15px] !border-none !rounded-xl !bg-gray-50 !px-4 focus:!bg-white focus:!shadow-[inset_0_0_0_2px_#2196F3] transition-all duration-300"
            />
          </Form.Item>
        </Form>
      </div>
    </div>
  );
}
